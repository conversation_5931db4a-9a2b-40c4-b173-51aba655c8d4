<svg width="1920" height="494" viewBox="0 0 1920 494" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_b_201_11645)">
        <path d="M-555.158 -935.552L206.014 299.32C272.103 406.538 412.596 439.88 519.815 373.791L1754.69 -387.38C1861.9 -453.469 1895.25 -593.963 1829.16 -701.181L1067.99 -1936.05C1001.9 -2043.27 861.404 -2076.61 754.186 -2010.52L-480.686 -1249.35C-587.905 -1183.26 -621.247 -1042.77 -555.158 -935.552Z" fill="url(#paint0_linear_201_11645)"/>
    </g>
    <g filter="url(#filter1_b_201_11645)">
        <path d="M-365.85 -978.718L155.699 157.844C200.984 256.531 317.698 299.822 416.385 254.536L1552.95 -267.013C1651.63 -312.299 1694.93 -429.012 1649.64 -527.699L1128.09 -1664.26C1082.81 -1762.95 966.092 -1806.24 867.404 -1760.95L-269.158 -1239.4C-367.845 -1194.12 -411.136 -1077.41 -365.85 -978.718Z" fill="url(#paint1_linear_201_11645)"/>
    </g>
    <g filter="url(#filter2_b_201_11645)">
        <path d="M-199.592 -995.206L127.265 32.0959C155.644 121.29 250.956 170.59 340.15 142.211L1367.45 -184.646C1456.65 -213.025 1505.95 -308.337 1477.57 -397.531L1150.71 -1424.83C1122.33 -1514.03 1027.02 -1563.33 937.825 -1534.95L-89.4769 -1208.09C-178.671 -1179.71 -227.971 -1084.4 -199.592 -995.206Z" fill="url(#paint2_linear_201_11645)"/>
    </g>
    <g filter="url(#filter3_b_201_11645)">
        <path d="M-55.5683 -991.823L116.176 -78.4863C131.088 0.816985 207.465 53.0162 286.768 38.104L1200.1 -133.64C1279.41 -148.552 1331.61 -224.929 1316.7 -304.232L1144.95 -1217.57C1130.04 -1296.87 1053.66 -1349.07 974.359 -1334.16L61.022 -1162.42C-18.2812 -1147.5 -70.4805 -1071.13 -55.5683 -991.823Z" fill="url(#paint3_linear_201_11645)"/>
    </g>
    <defs>
        <filter id="filter0_b_201_11645" x="-594.982" y="-2050.35" width="2463.96" height="2463.96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.93731"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_201_11645"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_201_11645" result="shape"/>
        </filter>
        <filter id="filter1_b_201_11645" x="-389.688" y="-1784.79" width="2063.16" height="2063.16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.93731"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_201_11645"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_201_11645" result="shape"/>
        </filter>
        <filter id="filter2_b_201_11645" x="-213.49" y="-1548.85" width="1704.95" height="1704.95" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.93731"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_201_11645"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_201_11645" result="shape"/>
        </filter>
        <filter id="filter3_b_201_11645" x="-63.986" y="-1342.58" width="1389.1" height="1389.1" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.93731"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_201_11645"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_201_11645" result="shape"/>
        </filter>
        <linearGradient id="paint0_linear_201_11645" x1="850.924" y1="-972.878" x2="1824.39" y2="-447.912" gradientUnits="userSpaceOnUse">
            <stop stop-color="#19191C" stop-opacity="0"/>
            <stop offset="0.33" stop-color="#129CFF" stop-opacity="0.15"/>
            <stop offset="0.9" stop-color="#00D1FF" stop-opacity="0.55"/>
        </linearGradient>
        <linearGradient id="paint1_linear_201_11645" x1="841.17" y1="-862.941" x2="1618.95" y2="-311.486" gradientUnits="userSpaceOnUse">
            <stop stop-color="#19191C" stop-opacity="0"/>
            <stop offset="0.33" stop-color="#129CFF" stop-opacity="0.15"/>
            <stop offset="0.9" stop-color="#00D1FF" stop-opacity="0.55"/>
        </linearGradient>
        <linearGradient id="paint2_linear_201_11645" x1="821.024" y1="-769.324" x2="1428.59" y2="-215.763" gradientUnits="userSpaceOnUse">
            <stop stop-color="#19191C" stop-opacity="0"/>
            <stop offset="0.33" stop-color="#129CFF" stop-opacity="0.15"/>
            <stop offset="0.9" stop-color="#00D1FF" stop-opacity="0.55"/>
        </linearGradient>
        <linearGradient id="paint3_linear_201_11645" x1="793.987" y1="-691.327" x2="1255.7" y2="-153.842" gradientUnits="userSpaceOnUse">
            <stop stop-color="#19191C" stop-opacity="0"/>
            <stop offset="0.33" stop-color="#129CFF" stop-opacity="0.15"/>
            <stop offset="0.9" stop-color="#00D1FF" stop-opacity="0.55"/>
        </linearGradient>
    </defs>
</svg>
